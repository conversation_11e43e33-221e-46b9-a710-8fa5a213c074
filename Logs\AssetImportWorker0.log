Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.62f1c1 (b0109b07edb8) revision 11538587'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16159 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.62f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/nwu/MLLM/Virtual Agent
-logFile
Logs/AssetImportWorker0.log
-srvPort
4023
Successfully changed project path to: D:/nwu/MLLM/Virtual Agent
D:/nwu/MLLM/Virtual Agent
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13340]  Target information:

Player connection [13340]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1676467088 [EditorId] 1676467088 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13340]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1676467088 [EditorId] 1676467088 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13340]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1676467088 [EditorId] 1676467088 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13340] Host joined multi-casting on [***********:54997]...
Player connection [13340] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
Refreshing native plugins compatible for Editor in 14.91 ms, found 12 plugins.
Preloading 2 native plugins for Editor in 2.71 ms.
Initialize engine version: 2022.3.62f1c1 (b0109b07edb8)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/MLLM/Virtual Agent/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56984
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.038788 seconds.
- Loaded All Assemblies, in  0.846 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 362 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.607 seconds
Domain Reload Profiling: 1444ms
	BeginReloadAssembly (558ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (135ms)
		LoadAssemblies (517ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (131ms)
			TypeCache.Refresh (129ms)
				TypeCache.ScanAssembly (114ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (608ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (572ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (440ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (86ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.214 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () (at D:/PICO Unity Integration SDK-3.3.0-20250917/Editor/PXR_SDKBuildCheck.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: D:/PICO Unity Integration SDK-3.3.0-20250917/Editor/PXR_SDKBuildCheck.cs Line: 31)

Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.031 seconds
Domain Reload Profiling: 2187ms
	BeginReloadAssembly (223ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (17ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (114ms)
	LoadAllAssembliesAndSetupDomain (782ms)
		LoadAssemblies (694ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (242ms)
			TypeCache.Refresh (204ms)
				TypeCache.ScanAssembly (180ms)
			ScanForSourceGeneratedMonoScriptInfo (26ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1031ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (801ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (58ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (136ms)
			ProcessInitializeOnLoadAttributes (443ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (95ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.08 seconds
Refreshing native plugins compatible for Editor in 16.32 ms, found 12 plugins.
Preloading 2 native plugins for Editor in 0.15 ms.
Unloading 4746 Unused Serialized files (Serialized files now loaded: 0)
Unloading 23 unused Assets / (55.3 KB). Loaded Objects now: 5219.
Memory consumption went from 167.0 MB to 167.0 MB.
Total: 4.819800 ms (FindLiveObjects: 0.364800 ms CreateObjectMapping: 0.131400 ms MarkObjects: 4.178200 ms  DeleteObjects: 0.144400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 9576.073765 seconds.
  path: Assets/Models/Remy_Body_Normal.png
  artifactKey: Guid(ec58edcde69fe12459ad045975a497a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Body_Normal.png using Guid(ec58edcde69fe12459ad045975a497a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'c0a40c2ada40ef331ebbeb5f454986eb') in 0.243601 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Models/Remy_Hair_Opacity.png
  artifactKey: Guid(15d0d03902121e24089d8e14e67817cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Hair_Opacity.png using Guid(15d0d03902121e24089d8e14e67817cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '86e8f3950b90364bf85aff5802c664d5') in 0.022385 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Models/Remy_Hair_Specular.png
  artifactKey: Guid(50cc1b5b61abdc64c811c2dd0035c552) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Hair_Specular.png using Guid(50cc1b5b61abdc64c811c2dd0035c552) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'fa76b5dbed70e3d7c06b8ebe4f44c988') in 0.012503 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Models/Remy_Body_Gloss.png
  artifactKey: Guid(6c0b4b4a2121d07428db2c64400c7529) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Body_Gloss.png using Guid(6c0b4b4a2121d07428db2c64400c7529) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'f9fe36174a9d2b88a8d97297852a9ff7') in 0.052836 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Models/Remy_Top_Gloss.png
  artifactKey: Guid(51b4bd067b73196488ce29f7ddbe9833) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Top_Gloss.png using Guid(51b4bd067b73196488ce29f7ddbe9833) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'f7881c55ce1bb3d3379851e2369372fb') in 0.021096 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Models/Remy_Top_Normal.png
  artifactKey: Guid(0915d2fd2d5840843af29dafcf90dd62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Top_Normal.png using Guid(0915d2fd2d5840843af29dafcf90dd62) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'edad3f42b032734124a1f4b2c6fc7a09') in 0.025739 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Models/Remy_Hair_Diffuse.png
  artifactKey: Guid(f4054b17d613e714e855520cdf1af546) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Hair_Diffuse.png using Guid(f4054b17d613e714e855520cdf1af546) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'c672189ac2c9a57938f4024437cb3dfb') in 0.021132 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Models/Remy_Bottom_Normal.png
  artifactKey: Guid(c31e020e5168da3479aeae3655590324) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Bottom_Normal.png using Guid(c31e020e5168da3479aeae3655590324) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '14d8bf4e628953f9a57b1da072bf0291') in 0.026152 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Models/Remy_Shoes_Diffuse.png
  artifactKey: Guid(501aba89e5b756b478770a7245dd4060) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Shoes_Diffuse.png using Guid(501aba89e5b756b478770a7245dd4060) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'd38f457fdee225bdc0786c8f7e5cd399') in 0.021458 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Models/Remy_Top_Diffuse.png
  artifactKey: Guid(a35109d93838ab44486a46c6f6fe8535) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Top_Diffuse.png using Guid(a35109d93838ab44486a46c6f6fe8535) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '652676b86612703d70529a071dd7080b') in 0.020922 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Materials/New Material.mat
  artifactKey: Guid(2ee21f1dd1f340649af217924c05bc94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Materials/New Material.mat using Guid(2ee21f1dd1f340649af217924c05bc94) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '4f4a5c3d63da7de1239d593d85e44b2a') in 0.066650 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
