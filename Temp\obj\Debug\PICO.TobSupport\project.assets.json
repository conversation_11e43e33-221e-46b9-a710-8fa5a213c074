{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Pico.Spatializer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.PICO": "1.0.0"}, "compile": {"bin/placeholder/Pico.Spatializer.dll": {}}, "runtime": {"bin/placeholder/Pico.Spatializer.dll": {}}}, "Unity.XR.PICO/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.XR.PICO.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.PICO.dll": {}}}, "Unity.XR.PICO.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Pico.Spatializer": "1.0.0", "Unity.XR.PICO": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.PICO.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.PICO.Editor.dll": {}}}}}, "libraries": {"Pico.Spatializer/1.0.0": {"type": "project", "path": "Pico.Spatializer.csproj", "msbuildProject": "Pico.Spatializer.csproj"}, "Unity.XR.PICO/1.0.0": {"type": "project", "path": "Unity.XR.PICO.csproj", "msbuildProject": "Unity.XR.PICO.csproj"}, "Unity.XR.PICO.Editor/1.0.0": {"type": "project", "path": "Unity.XR.PICO.Editor.csproj", "msbuildProject": "Unity.XR.PICO.Editor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.XR.PICO >= 1.0.0", "Unity.XR.PICO.Editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj", "projectName": "PICO.TobSupport", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\PICO.TobSupport\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}