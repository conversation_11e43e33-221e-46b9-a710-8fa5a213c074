Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.62f1c1 (b0109b07edb8) revision 11538587'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit CoreCountrySpecific' Language: 'zh' Physical Memory: 16159 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.62f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/nwu/MLLM/Virtual Agent
-logFile
Logs/AssetImportWorker1.log
-srvPort
4023
Successfully changed project path to: D:/nwu/MLLM/Virtual Agent
D:/nwu/MLLM/Virtual Agent
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [14216]  Target information:

Player connection [14216]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1679745010 [EditorId] 1679745010 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [14216]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1679745010 [EditorId] 1679745010 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [14216]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1679745010 [EditorId] 1679745010 [Version] 1048832 [Id] WindowsEditor(7,PC-SHENSHEN) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [14216] Host joined multi-casting on [***********:54997]...
Player connection [14216] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
Refreshing native plugins compatible for Editor in 15.72 ms, found 12 plugins.
Preloading 2 native plugins for Editor in 2.39 ms.
Initialize engine version: 2022.3.62f1c1 (b0109b07edb8)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/nwu/MLLM/Virtual Agent/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2860)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.6624
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56756
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.62f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.034919 seconds.
- Loaded All Assemblies, in  0.847 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 342 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.585 seconds
Domain Reload Profiling: 1426ms
	BeginReloadAssembly (535ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (122ms)
	LoadAllAssembliesAndSetupDomain (145ms)
		LoadAssemblies (513ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (139ms)
			TypeCache.Refresh (138ms)
				TypeCache.ScanAssembly (125ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (586ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (552ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (420ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (89ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.247 seconds
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
PXRLog [Build Check]RegisterBuildPlayerHandler,Already Do not show: False
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
Unity.XR.PXR.Editor.PXR_SDKBuildCheck:.cctor () (at D:/PICO Unity Integration SDK-3.3.0-20250917/Editor/PXR_SDKBuildCheck.cs:31)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: D:/PICO Unity Integration SDK-3.3.0-20250917/Editor/PXR_SDKBuildCheck.cs Line: 31)

Mono: successfully reloaded assembly
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  0.987 seconds
Domain Reload Profiling: 2209ms
	BeginReloadAssembly (224ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (18ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (133ms)
	LoadAllAssembliesAndSetupDomain (822ms)
		LoadAssemblies (724ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (223ms)
				TypeCache.ScanAssembly (200ms)
			ScanForSourceGeneratedMonoScriptInfo (24ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (988ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (784ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (48ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (119ms)
			ProcessInitializeOnLoadAttributes (482ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (71ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 23.49 ms, found 12 plugins.
Preloading 2 native plugins for Editor in 0.20 ms.
Unloading 4746 Unused Serialized files (Serialized files now loaded: 0)
Unloading 23 unused Assets / (55.2 KB). Loaded Objects now: 5219.
Memory consumption went from 167.1 MB to 167.0 MB.
Total: 5.902600 ms (FindLiveObjects: 0.544000 ms CreateObjectMapping: 0.159100 ms MarkObjects: 4.999200 ms  DeleteObjects: 0.198100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 9576.022656 seconds.
  path: Assets/Models/Remy_Shoes_Normal.png
  artifactKey: Guid(8dafdd22dc8dd9c4e85791c4400977c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Shoes_Normal.png using Guid(8dafdd22dc8dd9c4e85791c4400977c2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '9d035febfa95362f1ffe4c4d6de3c63e') in 0.207910 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Models/Remy_Shoes_Specular.png
  artifactKey: Guid(c34d2cc0c72cf4347bce1e32716b5dae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Shoes_Specular.png using Guid(c34d2cc0c72cf4347bce1e32716b5dae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '7594276b5488597795b993018a46bb41') in 0.018815 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Models/Remy_Bottom_Gloss.png
  artifactKey: Guid(414f9fd966847e946ad65652aba6102b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Bottom_Gloss.png using Guid(414f9fd966847e946ad65652aba6102b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '2b1a4b31a269a28cde256fd9837a74ad') in 0.019553 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Models/Remy_Shoes_Gloss.png
  artifactKey: Guid(7f56b95f4d1f3974a97d7296a1051777) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy_Shoes_Gloss.png using Guid(7f56b95f4d1f3974a97d7296a1051777) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: 'cefa020486903334be691d8be2600c8c') in 0.019009 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Models/Remy.fbx
  artifactKey: Guid(e685cd6a692973b419f1ba76ee6daee3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Models/Remy.fbx using Guid(e685cd6a692973b419f1ba76ee6daee3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 28 workers.
 -> (artifact id: '96fbdf0d3f248fd28dd8a53b236a47c4') in 0.500773 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 183
