{"format": 1, "restore": {"D:\\nwu\\MLLM\\Virtual Agent\\Assembly-CSharp.csproj": {}}, "projects": {"D:\\nwu\\MLLM\\Virtual Agent\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.Editor.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Editor.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Example.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Example.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj", "projectName": "PICO.Platform", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\PICO.Platform\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.Editor.csproj", "projectName": "PICO.Platform.Editor", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\PICO.Platform.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj", "projectName": "Pico.Spatializer", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Pico.Spatializer\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Editor.csproj", "projectName": "Pico.Spatializer.Editor", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Pico.Spatializer.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Example.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Example.csproj", "projectName": "Pico.Spatializer.Example", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Example.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Pico.Spatializer.Example\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj", "projectName": "PICO.TobSupport", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\PICO.TobSupport\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj", "projectName": "Unity.XR.PICO", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Unity.XR.PICO\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj", "projectName": "Unity.XR.PICO.Editor", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Unity.XR.PICO.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}