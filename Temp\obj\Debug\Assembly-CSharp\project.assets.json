{"version": 3, "targets": {".NETStandard,Version=v2.1": {"PICO.Platform/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.PICO": "1.0.0", "Unity.XR.PICO.Editor": "1.0.0"}, "compile": {"bin/placeholder/PICO.Platform.dll": {}}, "runtime": {"bin/placeholder/PICO.Platform.dll": {}}}, "PICO.Platform.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"PICO.Platform": "1.0.0", "Unity.XR.PICO": "1.0.0", "Unity.XR.PICO.Editor": "1.0.0"}, "compile": {"bin/placeholder/PICO.Platform.Editor.dll": {}}, "runtime": {"bin/placeholder/PICO.Platform.Editor.dll": {}}}, "Pico.Spatializer/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.PICO": "1.0.0"}, "compile": {"bin/placeholder/Pico.Spatializer.dll": {}}, "runtime": {"bin/placeholder/Pico.Spatializer.dll": {}}}, "Pico.Spatializer.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Pico.Spatializer": "1.0.0"}, "compile": {"bin/placeholder/Pico.Spatializer.Editor.dll": {}}, "runtime": {"bin/placeholder/Pico.Spatializer.Editor.dll": {}}}, "Pico.Spatializer.Example/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Pico.Spatializer": "1.0.0"}, "compile": {"bin/placeholder/Pico.Spatializer.Example.dll": {}}, "runtime": {"bin/placeholder/Pico.Spatializer.Example.dll": {}}}, "PICO.TobSupport/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.XR.PICO": "1.0.0", "Unity.XR.PICO.Editor": "1.0.0"}, "compile": {"bin/placeholder/PICO.TobSupport.dll": {}}, "runtime": {"bin/placeholder/PICO.TobSupport.dll": {}}}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.dll": {}}}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.dll": {}}}, "Unity.XR.PICO/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.XR.PICO.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.PICO.dll": {}}}, "Unity.XR.PICO.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Pico.Spatializer": "1.0.0", "Unity.XR.PICO": "1.0.0"}, "compile": {"bin/placeholder/Unity.XR.PICO.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.XR.PICO.Editor.dll": {}}}}}, "libraries": {"PICO.Platform/1.0.0": {"type": "project", "path": "PICO.Platform.csproj", "msbuildProject": "PICO.Platform.csproj"}, "PICO.Platform.Editor/1.0.0": {"type": "project", "path": "PICO.Platform.Editor.csproj", "msbuildProject": "PICO.Platform.Editor.csproj"}, "Pico.Spatializer/1.0.0": {"type": "project", "path": "Pico.Spatializer.csproj", "msbuildProject": "Pico.Spatializer.csproj"}, "Pico.Spatializer.Editor/1.0.0": {"type": "project", "path": "Pico.Spatializer.Editor.csproj", "msbuildProject": "Pico.Spatializer.Editor.csproj"}, "Pico.Spatializer.Example/1.0.0": {"type": "project", "path": "Pico.Spatializer.Example.csproj", "msbuildProject": "Pico.Spatializer.Example.csproj"}, "PICO.TobSupport/1.0.0": {"type": "project", "path": "PICO.TobSupport.csproj", "msbuildProject": "PICO.TobSupport.csproj"}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj"}, "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor/1.0.0": {"type": "project", "path": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "msbuildProject": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj"}, "Unity.XR.PICO/1.0.0": {"type": "project", "path": "Unity.XR.PICO.csproj", "msbuildProject": "Unity.XR.PICO.csproj"}, "Unity.XR.PICO.Editor/1.0.0": {"type": "project", "path": "Unity.XR.PICO.Editor.csproj", "msbuildProject": "Unity.XR.PICO.Editor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["PICO.Platform >= 1.0.0", "PICO.Platform.Editor >= 1.0.0", "PICO.TobSupport >= 1.0.0", "Pico.Spatializer >= 1.0.0", "Pico.Spatializer.Editor >= 1.0.0", "Pico.Spatializer.Example >= 1.0.0", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets >= 1.0.0", "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor >= 1.0.0", "Unity.XR.PICO >= 1.0.0", "Unity.XR.PICO.Editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\nwu\\MLLM\\Virtual Agent\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\nwu\\MLLM\\Virtual Agent\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.Platform.Editor.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Editor.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Example.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Pico.Spatializer.Example.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\PICO.TobSupport.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.csproj"}, "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj": {"projectPath": "D:\\nwu\\MLLM\\Virtual Agent\\Unity.XR.PICO.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}